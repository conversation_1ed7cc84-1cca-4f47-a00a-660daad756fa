# API Key Spider

一个用于从代码仓库、搜索引擎等多个数据源中搜索和发现各种AI服务API密钥的工具。

## 功能特性

- **多数据源搜索**：支持GitHub代码仓库搜索
- **智能模式匹配**：识别Gemini、OpenRouter、OpenAI、Claude等API Key格式
- **结果处理**：自动去重、置信度评估、格式化输出
- **安全合规**：请求限流、隐私保护、遵守API使用条款

## 支持的API Key类型

- **Gemini**: `AIzaSy[A-Za-z0-9_-]{33}`
- **OpenRouter**: `sk-or-[A-Za-z0-9]{48}`
- **OpenAI**: `sk-[A-Za-z0-9]{48}`
- **Claude**: `sk-ant-[A-Za-z0-9_-]{95}`
- **HuggingFace**: `hf_[A-Za-z0-9]{37}`
- **Cohere**: `[A-Za-z0-9]{40}`

## 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd apikey-spider
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量（可选）：
```bash
cp .env.example .env
# 编辑 .env 文件，添加你的API tokens
```

## 使用方法

### 基本用法

```bash
# 搜索GitHub中的Gemini API Key
python main.py --sources github --keywords "gemini api key" --max-results 50

# 使用多个关键词搜索
python main.py --keywords "openai api key,claude api key" --max-results 100

# 输出为CSV格式
python main.py --output-format csv --output-file results.csv

# 同时输出JSON和CSV
python main.py --output-format both
```

### 命令行参数

- `--sources, -s`: 搜索源 (github,google,bing 或 all)
- `--keywords, -k`: 搜索关键词，用逗号分隔
- `--max-results, -m`: 最大结果数 (默认: 100)
- `--output-format, -f`: 输出格式 (json|csv|both)
- `--output-file, -o`: 输出文件名
- `--config-file, -c`: 配置文件路径

### 配置GitHub Token

为了获得更好的搜索结果，建议配置GitHub Personal Access Token：

1. 在GitHub创建Personal Access Token
2. 在 `.env` 文件中设置：
```
GITHUB_TOKEN=your_github_token_here
```

## 输出格式

### JSON格式
```json
{
  "timestamp": "2025-01-01T12:00:00",
  "total_keys": 5,
  "keys": [
    {
      "key_type": "gemini",
      "masked_key": "AIza*******************************xOQx",
      "line_number": 10,
      "context": "GEMINI_API_KEY = \"AIzaSy...\"",
      "confidence": 0.95
    }
  ]
}
```

### CSV格式
```csv
Key Type,Masked Key,Line Number,Context,Confidence
gemini,AIza*******************************xOQx,10,"GEMINI_API_KEY = ""AIzaSy...""",0.95
```

## 测试模式匹配

运行测试脚本验证模式匹配功能：

```bash
python test_patterns.py
```

## 注意事项

- 本工具仅用于安全研究和合规检查
- 请遵守相关API的使用条款和限制
- 发现的API Key会被自动遮蔽以保护隐私
- 建议设置合理的请求间隔避免被限制

## 项目结构

```
apikey-spider/
├── main.py              # 主程序入口
├── config.py            # 配置管理
├── requirements.txt     # 依赖包
├── searchers/           # 搜索引擎模块
├── patterns/            # 模式匹配模块
├── processors/          # 数据处理模块
├── utils/              # 工具模块
└── results/            # 输出结果目录
```
