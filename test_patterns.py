"""
测试API Key模式匹配功能
"""
from config import Config
from patterns.matchers import APIKeyMatcher

def test_pattern_matching():
    """测试模式匹配功能"""
    config = Config()
    matcher = APIKeyMatcher(config)
    
    # 测试内容包含各种API Key
    test_content = """
# 这是一个测试文件
GEMINI_API_KEY = "AIzaSyBvOiM9OiI-cOekMJzQp-KQx9WQxOQxOQx"
OPENROUTER_KEY = "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"
OPENAI_API_KEY = "sk-1234567890abcdef1234567890abcdef1234567890abcdef12"
CLAUDE_KEY = "sk-ant-api03-1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef123456"
HUGGINGFACE_TOKEN = "hf_1234567890abcdef1234567890abcdef123456"

# 这些是示例，不应该匹配
EXAMPLE_KEY = "your_api_key_here"
TEST_KEY = "sk-test1234567890"
"""
    
    print("开始测试API Key模式匹配...")
    print("=" * 50)
    
    # 查找API Keys
    matches = matcher.find_api_keys(test_content)
    
    print(f"发现 {len(matches)} 个API Key:")
    print()
    
    for match in matches:
        print(f"类型: {match.key_type}")
        print(f"遮蔽Key: {match.masked_key}")
        print(f"行号: {match.line_number}")
        print(f"上下文: {match.context}")
        print(f"置信度: {match.confidence:.2f}")
        print("-" * 30)
    
    # 显示支持的类型
    print("\n支持的API Key类型:")
    for key_type in matcher.get_supported_types():
        pattern_info = matcher.get_pattern_info(key_type)
        print(f"- {key_type}: {pattern_info.get('description', 'N/A')}")

if __name__ == "__main__":
    test_pattern_matching()
