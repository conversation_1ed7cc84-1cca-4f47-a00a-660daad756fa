"""
结果去重处理器
"""
from typing import List, Set, Dict, Any
from patterns.matchers import APIKeyMatch
from searchers.base import SearchResult
import hashlib


class ResultDeduplicator:
    """结果去重器"""
    
    def __init__(self):
        self.seen_keys: Set[str] = set()
        self.seen_urls: Set[str] = set()
        self.seen_content_hashes: Set[str] = set()
    
    def deduplicate_api_keys(self, matches: List[APIKeyMatch]) -> List[APIKeyMatch]:
        """去重API Key匹配结果"""
        unique_matches = []
        
        for match in matches:
            # 创建唯一标识符
            key_id = f"{match.key_type}:{match.key_value}"
            
            if key_id not in self.seen_keys:
                self.seen_keys.add(key_id)
                unique_matches.append(match)
        
        return unique_matches
    
    def deduplicate_search_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """去重搜索结果"""
        unique_results = []
        
        for result in results:
            # 检查URL去重
            if result.url in self.seen_urls:
                continue
            
            # 检查内容去重
            content_hash = self._get_content_hash(result.content)
            if content_hash in self.seen_content_hashes:
                continue
            
            self.seen_urls.add(result.url)
            self.seen_content_hashes.add(content_hash)
            unique_results.append(result)
        
        return unique_results
    
    def _get_content_hash(self, content: str) -> str:
        """获取内容哈希值"""
        # 标准化内容（去除空白字符）
        normalized = ''.join(content.split())
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, int]:
        """获取去重统计信息"""
        return {
            'unique_keys': len(self.seen_keys),
            'unique_urls': len(self.seen_urls),
            'unique_contents': len(self.seen_content_hashes)
        }
    
    def reset(self):
        """重置去重器状态"""
        self.seen_keys.clear()
        self.seen_urls.clear()
        self.seen_content_hashes.clear()
