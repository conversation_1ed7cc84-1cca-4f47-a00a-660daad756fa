"""
API Key模式匹配器
"""
import re
from typing import List, Dict, Any, Tuple
from config import Config


class APIKeyMatch:
    """API Key匹配结果"""
    def __init__(self, key_type: str, key_value: str, line_number: int, 
                 context: str, confidence: float = 1.0):
        self.key_type = key_type
        self.key_value = key_value
        self.line_number = line_number
        self.context = context
        self.confidence = confidence
        self.masked_key = self._mask_key(key_value)
    
    def _mask_key(self, key: str) -> str:
        """遮蔽API Key敏感部分"""
        if len(key) <= 8:
            return key[:2] + "*" * (len(key) - 4) + key[-2:]
        return key[:4] + "*" * (len(key) - 8) + key[-4:]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'key_type': self.key_type,
            'masked_key': self.masked_key,
            'line_number': self.line_number,
            'context': self.context,
            'confidence': self.confidence
        }


class APIKeyMatcher:
    """API Key模式匹配器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.patterns = self._compile_patterns()
    
    def _compile_patterns(self) -> Dict[str, re.Pattern]:
        """编译正则表达式模式"""
        compiled = {}
        for key_type, pattern_info in self.config.API_KEY_PATTERNS.items():
            try:
                compiled[key_type] = re.compile(pattern_info['pattern'])
            except re.error as e:
                print(f"编译模式失败 {key_type}: {e}")
        return compiled
    
    def find_api_keys(self, content: str) -> List[APIKeyMatch]:
        """在内容中查找API Key"""
        matches = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # 跳过注释行和明显的示例
            if self._is_likely_example(line):
                continue
            
            for key_type, pattern in self.patterns.items():
                for match in pattern.finditer(line):
                    key_value = match.group(0)
                    
                    # 计算置信度
                    confidence = self._calculate_confidence(key_value, line, key_type)
                    
                    if confidence > 0.3:  # 置信度阈值
                        api_match = APIKeyMatch(
                            key_type=key_type,
                            key_value=key_value,
                            line_number=line_num,
                            context=line.strip(),
                            confidence=confidence
                        )
                        matches.append(api_match)
        
        return matches
    
    def _is_likely_example(self, line: str) -> bool:
        """判断是否为示例代码"""
        line_lower = line.lower()
        example_indicators = [
            'example', 'sample', 'demo', 'test', 'placeholder',
            'your_api_key', 'your-api-key', 'api_key_here',
            'replace_with', 'insert_your', 'put_your',
            '# ', '//', '/*', '*/', '<!--', '-->'
        ]
        
        return any(indicator in line_lower for indicator in example_indicators)
    
    def _calculate_confidence(self, key_value: str, context: str, key_type: str) -> float:
        """计算匹配置信度"""
        confidence = 1.0
        context_lower = context.lower()
        
        # 降低置信度的因素
        if any(word in context_lower for word in ['example', 'sample', 'demo', 'test']):
            confidence *= 0.3
        
        if any(word in context_lower for word in ['placeholder', 'your_api_key', 'replace']):
            confidence *= 0.1
        
        # 提高置信度的因素
        if any(word in context_lower for word in ['api_key', 'token', 'secret']):
            confidence *= 1.2
        
        if key_type in context_lower:
            confidence *= 1.3
        
        # 检查key格式的合理性
        if self._is_valid_key_format(key_value, key_type):
            confidence *= 1.1
        else:
            confidence *= 0.8
        
        return min(confidence, 1.0)
    
    def _is_valid_key_format(self, key_value: str, key_type: str) -> bool:
        """检查key格式是否合理"""
        # 检查是否包含过多重复字符
        if len(set(key_value)) < len(key_value) * 0.3:
            return False
        
        # 检查是否为明显的测试值
        test_patterns = ['1234', 'abcd', 'test', 'example']
        if any(pattern in key_value.lower() for pattern in test_patterns):
            return False
        
        return True
    
    def get_supported_types(self) -> List[str]:
        """获取支持的API Key类型"""
        return list(self.config.API_KEY_PATTERNS.keys())
    
    def get_pattern_info(self, key_type: str) -> Dict[str, str]:
        """获取模式信息"""
        return self.config.API_KEY_PATTERNS.get(key_type, {})
