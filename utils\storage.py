"""
数据存储工具
"""
import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any
from patterns.matchers import APIKeyMatch
from searchers.base import SearchResult


class ResultStorage:
    """结果存储器"""
    
    def __init__(self, output_dir: str = 'results'):
        self.output_dir = output_dir
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def save_api_keys_json(self, matches: List[APIKeyMatch], filename: str = None) -> str:
        """保存API Key结果为JSON格式"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'api_keys_{timestamp}.json'
        
        filepath = os.path.join(self.output_dir, filename)
        
        data = {
            'timestamp': datetime.now().isoformat(),
            'total_keys': len(matches),
            'keys': [match.to_dict() for match in matches]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def save_api_keys_csv(self, matches: List[APIKeyMatch], filename: str = None) -> str:
        """保存API Key结果为CSV格式"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'api_keys_{timestamp}.csv'
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(['Key Type', 'Masked Key', 'Line Number', 'Context', 'Confidence'])
            
            # 写入数据
            for match in matches:
                writer.writerow([
                    match.key_type,
                    match.masked_key,
                    match.line_number,
                    match.context,
                    f"{match.confidence:.2f}"
                ])
        
        return filepath
    
    def save_search_results(self, results: List[SearchResult], filename: str = None) -> str:
        """保存搜索结果"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'search_results_{timestamp}.json'
        
        filepath = os.path.join(self.output_dir, filename)
        
        data = {
            'timestamp': datetime.now().isoformat(),
            'total_results': len(results),
            'results': [result.to_dict() for result in results]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def save_summary_report(self, summary_data: Dict[str, Any], filename: str = None) -> str:
        """保存汇总报告"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'summary_report_{timestamp}.json'
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)
        
        return filepath
    
    def load_api_keys(self, filepath: str) -> List[Dict[str, Any]]:
        """加载API Key结果"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('keys', [])
    
    def get_output_files(self) -> List[str]:
        """获取输出目录中的所有文件"""
        if not os.path.exists(self.output_dir):
            return []
        
        files = []
        for filename in os.listdir(self.output_dir):
            filepath = os.path.join(self.output_dir, filename)
            if os.path.isfile(filepath):
                files.append(filepath)
        
        return sorted(files, key=os.path.getmtime, reverse=True)
