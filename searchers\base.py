"""
基础搜索器抽象类
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import asyncio
import aiohttp
import time
from config import Config


class SearchResult:
    """搜索结果数据类"""
    def __init__(self, content: str, url: str, source: str, file_path: str = "", line_number: int = 0):
        self.content = content
        self.url = url
        self.source = source
        self.file_path = file_path
        self.line_number = line_number
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'content': self.content,
            'url': self.url,
            'source': self.source,
            'file_path': self.file_path,
            'line_number': self.line_number,
            'timestamp': self.timestamp
        }


class BaseSearcher(ABC):
    """搜索器基类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.last_request_time = 0
    
    async def __aenter__(self):
        # 配置代理
        connector = None
        if self.config.HTTP_PROXY or self.config.HTTPS_PROXY:
            connector = aiohttp.TCPConnector()

        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.TIMEOUT),
            connector=connector
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _rate_limit(self):
        """请求限流"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.config.REQUEST_DELAY:
            await asyncio.sleep(self.config.REQUEST_DELAY - time_since_last)
        self.last_request_time = time.time()
    
    async def _make_request(self, url: str, headers: Dict[str, str] = None,
                          params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """发起HTTP请求"""
        await self._rate_limit()

        # 配置代理
        proxy = None
        if self.config.HTTP_PROXY:
            proxy = self.config.HTTP_PROXY
        elif self.config.HTTPS_PROXY:
            proxy = self.config.HTTPS_PROXY

        for attempt in range(self.config.MAX_RETRIES):
            try:
                async with self.session.get(url, headers=headers, params=params, proxy=proxy) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 403:
                        print(f"API限制: {response.status}")
                        return None
                    elif response.status == 429:
                        print(f"请求过于频繁，等待重试...")
                        await asyncio.sleep(2 ** attempt)
                        continue
                    else:
                        print(f"请求失败: {response.status}")
                        return None
            except Exception as e:
                print(f"请求异常 (尝试 {attempt + 1}/{self.config.MAX_RETRIES}): {e}")
                if attempt < self.config.MAX_RETRIES - 1:
                    await asyncio.sleep(2 ** attempt)
        
        return None
    
    @abstractmethod
    async def search(self, keywords: List[str], max_results: int = 100) -> List[SearchResult]:
        """搜索方法，子类必须实现"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取搜索器名称"""
        pass
