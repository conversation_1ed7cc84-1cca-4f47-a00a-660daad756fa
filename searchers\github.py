"""
GitHub代码搜索器
"""
from typing import List, Dict, Any
import base64
from .base import BaseSearcher, SearchResult
from config import Config


class GitHubSearcher(BaseSearcher):
    """GitHub代码搜索器"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.api_url = config.GITHUB_API_URL
        self.token = config.GITHUB_TOKEN
        
    def get_name(self) -> str:
        return "GitHub"
    
    def _get_headers(self) -> Dict[str, str]:
        """获取GitHub API请求头"""
        headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'APIKey-Spider/1.0'
        }
        if self.token:
            headers['Authorization'] = f'token {self.token}'
        return headers
    
    async def search(self, keywords: List[str], max_results: int = 100) -> List[SearchResult]:
        """搜索GitHub代码"""
        results = []
        
        for keyword in keywords:
            print(f"搜索GitHub: {keyword}")
            
            # 构建搜索查询 - 简化查询避免422错误
            if len(keyword) < 10:  # 短关键词直接搜索
                query = keyword
            else:
                query = f'"{keyword}"'
            
            params = {
                'q': query,
                'sort': 'indexed',
                'order': 'desc',
                'per_page': min(100, max_results),
                'page': 1
            }
            
            # 搜索代码
            search_url = f"{self.api_url}/search/code"
            response = await self._make_request(search_url, self._get_headers(), params)
            
            if not response or 'items' not in response:
                continue
            
            # 处理搜索结果
            for item in response['items'][:max_results]:
                try:
                    # 获取文件内容
                    content_result = await self._get_file_content(item)
                    if content_result:
                        results.append(content_result)
                        
                        if len(results) >= max_results:
                            break
                            
                except Exception as e:
                    print(f"处理文件失败 {item.get('name', 'unknown')}: {e}")
                    continue
            
            if len(results) >= max_results:
                break
        
        return results
    
    async def _get_file_content(self, item: Dict[str, Any]) -> SearchResult:
        """获取文件内容"""
        try:
            # 获取文件详细信息
            content_url = item['url']
            response = await self._make_request(content_url, self._get_headers())
            
            if not response:
                return None
            
            # 解码文件内容
            content = ""
            if response.get('encoding') == 'base64':
                try:
                    content = base64.b64decode(response['content']).decode('utf-8')
                except:
                    content = response.get('content', '')
            else:
                content = response.get('content', '')
            
            # 创建搜索结果
            return SearchResult(
                content=content,
                url=item['html_url'],
                source=self.get_name(),
                file_path=item['path'],
                line_number=0
            )
            
        except Exception as e:
            print(f"获取文件内容失败: {e}")
            return None
    
    async def search_repositories(self, keywords: List[str], max_results: int = 50) -> List[Dict[str, Any]]:
        """搜索相关仓库"""
        results = []
        
        for keyword in keywords:
            query = f'"{keyword}" in:readme OR "{keyword}" in:description'
            params = {
                'q': query,
                'sort': 'stars',
                'order': 'desc',
                'per_page': min(30, max_results)
            }
            
            search_url = f"{self.api_url}/search/repositories"
            response = await self._make_request(search_url, self._get_headers(), params)
            
            if response and 'items' in response:
                results.extend(response['items'][:max_results])
        
        return results
