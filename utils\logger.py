"""
日志工具
"""
import logging
import sys
from datetime import datetime
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.MAGENTA
    }
    
    def format(self, record):
        # 添加颜色
        color = self.COLORS.get(record.levelname, '')
        record.levelname = f"{color}{record.levelname}{Style.RESET_ALL}"
        
        # 格式化时间
        record.asctime = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        return super().format(record)


def setup_logger(name: str = 'apikey-spider', level: str = 'INFO') -> logging.Logger:
    """设置日志器"""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # 格式化器
    formatter = ColoredFormatter(
        '%(asctime)s | %(levelname)s | %(message)s'
    )
    console_handler.setFormatter(formatter)
    
    logger.addHandler(console_handler)
    
    return logger


def log_search_progress(current: int, total: int, source: str):
    """记录搜索进度"""
    logger = logging.getLogger('apikey-spider')
    percentage = (current / total) * 100 if total > 0 else 0
    logger.info(f"{source} 搜索进度: {current}/{total} ({percentage:.1f}%)")


def log_api_key_found(key_type: str, masked_key: str, source: str):
    """记录发现的API Key"""
    logger = logging.getLogger('apikey-spider')
    logger.info(f"发现 {Fore.YELLOW}{key_type}{Style.RESET_ALL} Key: {masked_key} (来源: {source})")


def log_search_summary(total_keys: int, total_sources: int, elapsed_time: float):
    """记录搜索总结"""
    logger = logging.getLogger('apikey-spider')
    logger.info(f"\n搜索完成:")
    logger.info(f"  发现API Key: {Fore.CYAN}{total_keys}{Style.RESET_ALL} 个")
    logger.info(f"  搜索来源: {total_sources} 个")
    logger.info(f"  耗时: {elapsed_time:.2f} 秒")
