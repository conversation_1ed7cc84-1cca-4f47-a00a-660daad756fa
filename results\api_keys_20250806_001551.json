{"timestamp": "2025-08-06T00:15:51.641112", "total_keys": 6, "keys": [{"key_type": "gemini", "masked_key": "AIza*******************************MqVU", "line_number": 1, "context": "var apikey = ['AIzaSyCt3DULzE2trDJhfFUosWZT-3GEObbMqVU', 'AIzaSyCsbx8BSyLwkw6XX6Lg5OF1U0HNtI9VmCY', 'AIzaSyBLMJAT6oqTZxAMsCsMjXzoo4lkJL4MmfM', 'AIzaSyCIY6fomcJxOt0XQ_naa1rzfd5wlOMGKDY'][Math.floor(Math.random() * 4)];", "confidence": 1.0}, {"key_type": "gemini", "masked_key": "AIza*******************************VmCY", "line_number": 1, "context": "var apikey = ['AIzaSyCt3DULzE2trDJhfFUosWZT-3GEObbMqVU', 'AIzaSyCsbx8BSyLwkw6XX6Lg5OF1U0HNtI9VmCY', 'AIzaSyBLMJAT6oqTZxAMsCsMjXzoo4lkJL4MmfM', 'AIzaSyCIY6fomcJxOt0XQ_naa1rzfd5wlOMGKDY'][Math.floor(Math.random() * 4)];", "confidence": 1.0}, {"key_type": "gemini", "masked_key": "AIza*******************************MmfM", "line_number": 1, "context": "var apikey = ['AIzaSyCt3DULzE2trDJhfFUosWZT-3GEObbMqVU', 'AIzaSyCsbx8BSyLwkw6XX6Lg5OF1U0HNtI9VmCY', 'AIzaSyBLMJAT6oqTZxAMsCsMjXzoo4lkJL4MmfM', 'AIzaSyCIY6fomcJxOt0XQ_naa1rzfd5wlOMGKDY'][Math.floor(Math.random() * 4)];", "confidence": 1.0}, {"key_type": "gemini", "masked_key": "AIza*******************************GKDY", "line_number": 1, "context": "var apikey = ['AIzaSyCt3DULzE2trDJhfFUosWZT-3GEObbMqVU', 'AIzaSyCsbx8BSyLwkw6XX6Lg5OF1U0HNtI9VmCY', 'AIzaSyBLMJAT6oqTZxAMsCsMjXzoo4lkJL4MmfM', 'AIzaSyCIY6fomcJxOt0XQ_naa1rzfd5wlOMGKDY'][Math.floor(Math.random() * 4)];", "confidence": 1.0}, {"key_type": "gemini", "masked_key": "AIza*******************************jdSU", "line_number": 24, "context": "AIzaSyAqL193sdtj8fQpeHyoXIg0DOWiI6ujdSU", "confidence": 1.0}, {"key_type": "gemini", "masked_key": "AIza*******************************358c", "line_number": 468, "context": "TENOR_API_KEY: 'AIzaSyCyouca1_KKy4W_MG1xsPzuku5oa8W358c',", "confidence": 1.0}]}