"""
API Key Spider - 主程序入口
"""
import asyncio
import click
import time
from typing import List
from config import Config
from searchers.github import GitHubSearcher
from searchers.playwright_searcher import PlaywrightSearcher
from patterns.matchers import APIKeyMatcher
from processors.deduplicator import ResultDeduplicator
from utils.logger import setup_logger, log_search_progress, log_api_key_found, log_search_summary
from utils.storage import ResultStorage


class APIKeySpider:
    """API Key爬虫主类"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logger(level=self.config.LOG_LEVEL)
        self.matcher = APIKeyMatcher(self.config)
        self.deduplicator = ResultDeduplicator()
        self.storage = ResultStorage(self.config.OUTPUT_DIR)
    
    async def search_api_keys(self, sources: List[str], keywords: List[str], 
                            max_results: int = 100) -> dict:
        """搜索API Keys"""
        start_time = time.time()
        all_matches = []
        search_stats = {}
        
        self.logger.info(f"开始搜索API Keys...")
        self.logger.info(f"搜索源: {', '.join(sources)}")
        self.logger.info(f"关键词: {', '.join(keywords)}")
        self.logger.info(f"最大结果数: {max_results}")
        
        # GitHub搜索
        if 'github' in sources:
            self.logger.info("正在搜索GitHub...")
            github_matches = await self._search_github(keywords, max_results)
            all_matches.extend(github_matches)
            search_stats['github'] = len(github_matches)

        # Playwright搜索（备用方案）
        if 'playwright' in sources or ('github' in sources and not all_matches):
            self.logger.info("正在使用Playwright搜索...")
            playwright_matches = await self._search_playwright(keywords, max_results)
            all_matches.extend(playwright_matches)
            search_stats['playwright'] = len(playwright_matches)
        
        # 去重处理
        unique_matches = self.deduplicator.deduplicate_api_keys(all_matches)
        
        # 按置信度排序
        unique_matches.sort(key=lambda x: x.confidence, reverse=True)
        
        # 记录发现的API Keys
        for match in unique_matches:
            log_api_key_found(match.key_type, match.masked_key, "GitHub")
        
        elapsed_time = time.time() - start_time
        log_search_summary(len(unique_matches), len(sources), elapsed_time)
        
        return {
            'matches': unique_matches,
            'stats': search_stats,
            'elapsed_time': elapsed_time,
            'dedup_stats': self.deduplicator.get_stats()
        }
    
    async def _search_github(self, keywords: List[str], max_results: int) -> List:
        """搜索GitHub"""
        matches = []
        
        async with GitHubSearcher(self.config) as searcher:
            # 搜索代码
            search_results = await searcher.search(keywords, max_results)
            
            self.logger.info(f"GitHub搜索到 {len(search_results)} 个文件")
            
            # 在搜索结果中查找API Keys
            for i, result in enumerate(search_results):
                log_search_progress(i + 1, len(search_results), "GitHub")
                
                # 使用模式匹配器查找API Keys
                file_matches = self.matcher.find_api_keys(result.content)
                
                # 为每个匹配添加来源信息
                for match in file_matches:
                    match.source_url = result.url
                    match.file_path = result.file_path
                
                matches.extend(file_matches)
        
        return matches

    async def _search_playwright(self, keywords: List[str], max_results: int) -> List:
        """使用Playwright搜索"""
        matches = []

        try:
            async with PlaywrightSearcher(self.config) as searcher:
                # 搜索代码
                search_results = await searcher.search(keywords, max_results)

                self.logger.info(f"Playwright搜索到 {len(search_results)} 个结果")

                # 在搜索结果中查找API Keys
                for i, result in enumerate(search_results):
                    log_search_progress(i + 1, len(search_results), "Playwright")

                    # 使用模式匹配器查找API Keys
                    file_matches = self.matcher.find_api_keys(result.content)

                    # 为每个匹配添加来源信息
                    for match in file_matches:
                        match.source_url = result.url
                        match.file_path = result.file_path

                    matches.extend(file_matches)

        except Exception as e:
            self.logger.error(f"Playwright搜索失败: {e}")

        return matches


@click.command()
@click.option('--sources', '-s', default='github',
              help='搜索源 (github,playwright,google,bing 或 all)')
@click.option('--keywords', '-k', 
              help='搜索关键词，用逗号分隔')
@click.option('--max-results', '-m', default=100, 
              help='最大结果数')
@click.option('--output-format', '-f', default='json',
              type=click.Choice(['json', 'csv', 'both']),
              help='输出格式')
@click.option('--output-file', '-o',
              help='输出文件名')
@click.option('--config-file', '-c',
              help='配置文件路径')
def main(sources, keywords, max_results, output_format, output_file, config_file):
    """API Key Spider - 搜索和发现API密钥"""
    
    # 解析搜索源
    if sources == 'all':
        source_list = ['github', 'playwright']
    else:
        source_list = [s.strip() for s in sources.split(',')]
    
    # 解析关键词
    if keywords:
        keyword_list = [k.strip() for k in keywords.split(',')]
    else:
        # 使用默认关键词
        config = Config()
        keyword_list = config.SEARCH_KEYWORDS
    
    # 创建爬虫实例
    spider = APIKeySpider()
    
    # 运行搜索
    async def run_search():
        results = await spider.search_api_keys(source_list, keyword_list, max_results)
        
        # 保存结果
        if results['matches']:
            if output_format in ['json', 'both']:
                json_file = spider.storage.save_api_keys_json(results['matches'], 
                                                            output_file if output_file and output_format == 'json' else None)
                spider.logger.info(f"结果已保存到: {json_file}")
            
            if output_format in ['csv', 'both']:
                csv_file = spider.storage.save_api_keys_csv(results['matches'],
                                                          output_file if output_file and output_format == 'csv' else None)
                spider.logger.info(f"结果已保存到: {csv_file}")
            
            # 保存汇总报告
            summary_data = {
                'search_params': {
                    'sources': source_list,
                    'keywords': keyword_list,
                    'max_results': max_results
                },
                'results': {
                    'total_matches': len(results['matches']),
                    'by_type': {},
                    'stats': results['stats'],
                    'elapsed_time': results['elapsed_time']
                }
            }
            
            # 统计各类型API Key数量
            for match in results['matches']:
                key_type = match.key_type
                summary_data['results']['by_type'][key_type] = summary_data['results']['by_type'].get(key_type, 0) + 1
            
            summary_file = spider.storage.save_summary_report(summary_data)
            spider.logger.info(f"汇总报告已保存到: {summary_file}")
        else:
            spider.logger.info("未发现任何API Key")
    
    # 运行异步任务
    asyncio.run(run_search())


if __name__ == '__main__':
    main()
