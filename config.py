"""
配置文件 - API Key Spider
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # GitHub API配置
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN', '')
    GITHUB_API_URL = 'https://api.github.com'

    # 代理配置
    HTTP_PROXY = os.getenv('HTTP_PROXY', '')
    HTTPS_PROXY = os.getenv('HTTPS_PROXY', '')
    SOCKS_PROXY = os.getenv('SOCKS_PROXY', '')

    # Google搜索配置
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', '')
    GOOGLE_CSE_ID = os.getenv('GOOGLE_CSE_ID', '')
    
    # 搜索配置
    MAX_RESULTS_PER_SEARCH = 100
    REQUEST_DELAY = 1.0  # 请求间隔（秒）
    MAX_RETRIES = 3
    TIMEOUT = 30
    
    # API Key模式配置
    API_KEY_PATTERNS = {
        'gemini': {
            'pattern': r'AIzaSy[A-Za-z0-9_-]{33}',
            'description': 'Google Gemini API Key'
        },
        'openrouter': {
            'pattern': r'sk-or-[A-Za-z0-9]{48}',
            'description': 'OpenRouter API Key'
        },
        'openai': {
            'pattern': r'sk-[A-Za-z0-9]{48}',
            'description': 'OpenAI API Key'
        },
        'claude': {
            'pattern': r'sk-ant-[A-Za-z0-9_-]{95}',
            'description': 'Anthropic Claude API Key'
        },
        'huggingface': {
            'pattern': r'hf_[A-Za-z0-9]{37}',
            'description': 'Hugging Face API Key'
        },
        'cohere': {
            'pattern': r'[A-Za-z0-9]{40}',
            'description': 'Cohere API Key'
        }
    }
    
    # 搜索关键词
    SEARCH_KEYWORDS = [
        'gemini api key',
        'openrouter api key', 
        'openai api key',
        'claude api key',
        'huggingface token',
        'cohere api key',
        'AIzaSy',
        'sk-or-',
        'sk-ant-',
        'hf_'
    ]
    
    # 输出配置
    OUTPUT_DIR = 'results'
    LOG_LEVEL = 'INFO'
    
    # 文件扩展名过滤
    SEARCH_FILE_EXTENSIONS = [
        '.py', '.js', '.ts', '.java', '.go', '.rs', '.cpp', '.c',
        '.php', '.rb', '.swift', '.kt', '.scala', '.sh', '.bat',
        '.json', '.yaml', '.yml', '.toml', '.ini', '.env',
        '.md', '.txt', '.cfg', '.conf'
    ]
