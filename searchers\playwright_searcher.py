"""
Playwright网页搜索器 - 备用搜索方案
"""
from typing import List, Dict, Any
import asyncio
from playwright.async_api import async_playwright, Browser, Page
from .base import BaseSearcher, SearchResult
from config import Config


class PlaywrightSearcher(BaseSearcher):
    """Playwright网页搜索器"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.browser: Browser = None
        self.page: Page = None
    
    def get_name(self) -> str:
        return "Playwright"
    
    async def __aenter__(self):
        self.playwright = await async_playwright().start()
        
        # 配置浏览器启动参数
        launch_options = {
            'headless': True,
            'args': ['--no-sandbox', '--disable-dev-shm-usage']
        }
        
        # 配置代理
        if self.config.HTTP_PROXY:
            proxy_url = self.config.HTTP_PROXY
            if proxy_url.startswith('http://'):
                proxy_url = proxy_url[7:]
            elif proxy_url.startswith('https://'):
                proxy_url = proxy_url[8:]
            
            if ':' in proxy_url:
                server, port = proxy_url.split(':', 1)
                launch_options['proxy'] = {
                    'server': f'http://{server}:{port}'
                }
        
        self.browser = await self.playwright.chromium.launch(**launch_options)
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def search(self, keywords: List[str], max_results: int = 100) -> List[SearchResult]:
        """使用Playwright搜索"""
        results = []
        
        for keyword in keywords:
            print(f"Playwright搜索: {keyword}")
            
            # 搜索GitHub
            github_results = await self._search_github_web(keyword, max_results // len(keywords))
            results.extend(github_results)
            
            if len(results) >= max_results:
                break
        
        return results[:max_results]
    
    async def _search_github_web(self, keyword: str, max_results: int) -> List[SearchResult]:
        """通过网页搜索GitHub"""
        results = []
        
        try:
            # 构建GitHub搜索URL
            search_url = f"https://github.com/search?q={keyword.replace(' ', '+')}&type=code"
            
            await self.page.goto(search_url, wait_until='networkidle')
            await asyncio.sleep(2)  # 等待页面加载
            
            # 获取搜索结果
            code_results = await self.page.query_selector_all('.code-list-item')
            
            for i, result_element in enumerate(code_results[:max_results]):
                try:
                    # 获取文件链接
                    link_element = await result_element.query_selector('a[data-testid="result-file-path"]')
                    if not link_element:
                        continue
                    
                    file_url = await link_element.get_attribute('href')
                    if not file_url:
                        continue
                    
                    file_url = f"https://github.com{file_url}"
                    
                    # 获取文件路径
                    file_path = await link_element.text_content()
                    
                    # 获取代码片段
                    code_element = await result_element.query_selector('.code-list-item-code')
                    code_content = ""
                    if code_element:
                        code_content = await code_element.text_content()
                    
                    if code_content:
                        search_result = SearchResult(
                            content=code_content,
                            url=file_url,
                            source=self.get_name(),
                            file_path=file_path or "",
                            line_number=0
                        )
                        results.append(search_result)
                
                except Exception as e:
                    print(f"处理搜索结果失败: {e}")
                    continue
            
        except Exception as e:
            print(f"GitHub网页搜索失败: {e}")
        
        return results
    
    async def _search_google_web(self, keyword: str, max_results: int) -> List[SearchResult]:
        """通过网页搜索Google"""
        results = []
        
        try:
            # 构建Google搜索URL，专门搜索代码相关内容
            search_query = f"{keyword} site:github.com OR site:gitlab.com filetype:py OR filetype:js OR filetype:java"
            search_url = f"https://www.google.com/search?q={search_query.replace(' ', '+')}"
            
            await self.page.goto(search_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            # 获取搜索结果
            search_results = await self.page.query_selector_all('div.g')
            
            for result_element in search_results[:max_results]:
                try:
                    # 获取标题和链接
                    title_element = await result_element.query_selector('h3')
                    link_element = await result_element.query_selector('a')
                    
                    if not title_element or not link_element:
                        continue
                    
                    title = await title_element.text_content()
                    url = await link_element.get_attribute('href')
                    
                    # 获取描述
                    desc_element = await result_element.query_selector('.VwiC3b')
                    description = ""
                    if desc_element:
                        description = await desc_element.text_content()
                    
                    if url and title:
                        search_result = SearchResult(
                            content=f"{title}\n{description}",
                            url=url,
                            source=self.get_name(),
                            file_path="",
                            line_number=0
                        )
                        results.append(search_result)
                
                except Exception as e:
                    print(f"处理Google搜索结果失败: {e}")
                    continue
        
        except Exception as e:
            print(f"Google网页搜索失败: {e}")
        
        return results
