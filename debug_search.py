"""
调试搜索功能
"""
import asyncio
from config import Config
from searchers.github import GitHubSearcher
from searchers.playwright_searcher import PlaywrightSearcher

async def test_github_search():
    """测试GitHub搜索"""
    print("测试GitHub搜索...")
    config = Config()
    
    async with GitHubSearcher(config) as searcher:
        try:
            results = await searcher.search(["AIzaSy"], 5)
            print(f"GitHub搜索结果: {len(results)} 个")
            for result in results:
                print(f"- {result.url}")
        except Exception as e:
            print(f"GitHub搜索错误: {e}")

async def test_playwright_search():
    """测试Playwright搜索"""
    print("\n测试Playwright搜索...")
    config = Config()
    
    try:
        async with PlaywrightSearcher(config) as searcher:
            results = await searcher.search(["AIzaSy"], 5)
            print(f"Playwright搜索结果: {len(results)} 个")
            for result in results:
                print(f"- {result.url}")
    except Exception as e:
        print(f"Playwright搜索错误: {e}")

async def main():
    """主函数"""
    await test_github_search()
    await test_playwright_search()

if __name__ == "__main__":
    asyncio.run(main())
